import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { LogOut, Menu, X, Shield, MessageSquare, Home, User } from 'lucide-react';

const Navbar = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMobileMenuOpen(false);
  };

  const isActive = (path) => location.pathname === path;

  const NavLink = ({ to, children, icon: Icon, onClick }) => (
    <Link
      to={to}
      onClick={onClick}
      className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
        isActive(to)
          ? 'bg-blue-600 text-white'
          : 'text-gray-300 hover:text-white hover:bg-gray-700'
      }`}
    >
      {Icon && <Icon size={18} />}
      <span>{children}</span>
    </Link>
  );

  return (
    <nav className="glass border-b border-gray-700 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">CM</span>
            </div>
            <span className="text-white font-bold text-xl">CommentMod</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            <NavLink to="/" icon={Home}>
              Home
            </NavLink>
            
            {isAuthenticated ? (
              <>
                <NavLink to="/comments" icon={MessageSquare}>
                  Comments
                </NavLink>
                
                {user?.role === 'admin' && (
                  <NavLink to="/admin" icon={Shield}>
                    Admin Panel
                  </NavLink>
                )}
                
                <div className="flex items-center space-x-4 ml-4 pl-4 border-l border-gray-600">
                  <div className="flex items-center space-x-2">
                    <User size={18} className="text-gray-400" />
                    <span className="text-gray-300">{user?.name}</span>
                    <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded-full">
                      {user?.role}
                    </span>
                  </div>
                  
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 text-gray-300 hover:text-white hover:bg-red-600 px-3 py-2 rounded-lg transition-all duration-200"
                  >
                    <LogOut size={18} />
                    <span>Logout</span>
                  </button>
                </div>
              </>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition-all duration-200"
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="btn-primary"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden text-gray-300 hover:text-white"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-700">
            <div className="flex flex-col space-y-2">
              <NavLink to="/" icon={Home} onClick={() => setIsMobileMenuOpen(false)}>
                Home
              </NavLink>
              
              {isAuthenticated ? (
                <>
                  <NavLink to="/comments" icon={MessageSquare} onClick={() => setIsMobileMenuOpen(false)}>
                    Comments
                  </NavLink>
                  
                  {user?.role === 'admin' && (
                    <NavLink to="/admin" icon={Shield} onClick={() => setIsMobileMenuOpen(false)}>
                      Admin Panel
                    </NavLink>
                  )}
                  
                  <div className="pt-4 border-t border-gray-700 mt-4">
                    <div className="flex items-center space-x-2 px-3 py-2 text-gray-300">
                      <User size={18} />
                      <span>{user?.name}</span>
                      <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded-full">
                        {user?.role}
                      </span>
                    </div>
                    
                    <button
                      onClick={handleLogout}
                      className="flex items-center space-x-2 text-gray-300 hover:text-white hover:bg-red-600 px-3 py-2 rounded-lg transition-all duration-200 w-full"
                    >
                      <LogOut size={18} />
                      <span>Logout</span>
                    </button>
                  </div>
                </>
              ) : (
                <div className="flex flex-col space-y-2 pt-4 border-t border-gray-700 mt-4">
                  <Link
                    to="/login"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="text-gray-300 hover:text-white px-3 py-2 rounded-lg transition-all duration-200"
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="btn-primary text-center"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
