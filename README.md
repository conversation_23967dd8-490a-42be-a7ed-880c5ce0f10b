# Comment Moderation System - Frontend

A modern React-based frontend for an AI-powered comment moderation system with sentiment analysis and toxicity detection.

## Features

### 🔐 Authentication & Authorization
- User registration and login
- Role-based access control (User/Admin)
- JWT token-based authentication
- Protected routes

### 👥 User Features
- Submit comments with real-time AI analysis
- View allowed comments with sentiment indicators
- Responsive design with dark theme
- Toast notifications for user feedback

### 🛡️ Admin Features
- Comprehensive admin dashboard
- Manage flagged, removed, and allowed comments
- Real-time comment statistics
- Bulk comment moderation actions
- Search and filter functionality

### 🤖 AI Integration
- Sentiment analysis with visual indicators
- Toxicity detection and scoring
- Automatic comment classification
- Real-time processing feedback

## Tech Stack

- **React 18** - Modern functional components with hooks
- **React Router** - Client-side routing
- **Tailwind CSS** - Utility-first styling
- **Axios** - HTTP client for API calls
- **React Hot Toast** - Toast notifications
- **Lucide React** - Beautiful icons
- **Vite** - Fast build tool and dev server

## Getting Started

### Prerequisites
- Node.js 16+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment variables:
```bash
cp .env.example .env
```
Edit `.env` and set your API URL:
```
REACT_APP_API_URL=http://localhost:3001/api
```

4. Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── CommentCard.jsx     # Individual comment display
│   ├── CommentForm.jsx     # Comment submission form
│   ├── CommentList.jsx     # List of comments
│   ├── CommentTable.jsx    # Admin table for comment management
│   ├── Navbar.jsx          # Navigation component
│   └── ProtectedRoute.jsx  # Route protection wrapper
├── contexts/            # React contexts
│   └── AuthContext.jsx     # Authentication state management
├── pages/              # Page components
│   ├── AdminPanel.jsx      # Admin dashboard
│   ├── Comments.jsx        # User comments page
│   ├── Home.jsx           # Landing page
│   ├── Login.jsx          # Login form
│   └── Register.jsx       # Registration form
├── services/           # API service layer
│   ├── api.js             # Axios configuration
│   ├── authService.js     # Authentication API calls
│   └── commentService.js  # Comment API calls
├── App.jsx             # Main app component with routing
├── main.jsx           # React app entry point
└── index.css          # Global styles and Tailwind imports
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## API Integration

The frontend expects a REST API with the following endpoints:

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/profile` - Get user profile

### Comments
- `GET /api/comments` - Get comments (with status filter)
- `POST /api/comments` - Create new comment
- `PUT /api/comments/:id/allow` - Allow flagged comment (admin)
- `PUT /api/comments/:id/remove` - Remove flagged comment (admin)
- `GET /api/comments/stats` - Get comment statistics (admin)

## Environment Variables

- `REACT_APP_API_URL` - Backend API base URL
- `NODE_ENV` - Environment (development/production)

## Design Features

### Modern UI/UX
- Dark theme with gradient backgrounds
- Glass morphism effects
- Smooth animations and transitions
- Responsive design for all devices
- Accessible color contrasts

### User Experience
- Loading states and error handling
- Real-time feedback with toast notifications
- Intuitive navigation with role-based menus
- Search and filter capabilities
- Pagination for large datasets

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
