import api from './api';

export const authService = {
  // Login user
  login: (email, password) => {
    return api.post('/auth/login', { email, password });
  },

  // Register user
  register: (userData) => {
    return api.post('/auth/register', userData);
  },

  // Logout user
  logout: () => {
    return api.post('/auth/logout');
  },

  // Get current user profile
  getProfile: () => {
    return api.get('/auth/profile');
  },

  // Refresh token
  refreshToken: () => {
    return api.post('/auth/refresh');
  },
};
