import api from './api';

export const commentService = {
  // Get comments with optional status filter
  getComments: (status = null, page = 1, limit = 10) => {
    const params = { page, limit };
    if (status) {
      params.status = status;
    }
    return api.get('/comments', { params });
  },

  // Create a new comment
  createComment: (content) => {
    return api.post('/comments', { content });
  },

  // Update comment status (admin only)
  updateCommentStatus: (commentId, status) => {
    return api.put(`/comments/${commentId}/status`, { status });
  },

  // Allow a comment (admin only)
  allowComment: (commentId) => {
    return api.put(`/comments/${commentId}/allow`);
  },

  // Remove a comment (admin only)
  removeComment: (commentId) => {
    return api.put(`/comments/${commentId}/remove`);
  },

  // Get comment by ID
  getCommentById: (commentId) => {
    return api.get(`/comments/${commentId}`);
  },

  // Delete comment permanently (admin only)
  deleteComment: (commentId) => {
    return api.delete(`/comments/${commentId}`);
  },

  // Get comment statistics (admin only)
  getCommentStats: () => {
    return api.get('/comments/stats');
  },
};
