import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { MessageSquare, Shield, Zap, Users, TrendingUp, CheckCircle } from 'lucide-react';

const Home = () => {
  const { isAuthenticated, user } = useAuth();

  const features = [
    {
      icon: MessageSquare,
      title: 'Smart Comment Moderation',
      description: 'AI-powered sentiment analysis and toxicity detection for all comments.',
    },
    {
      icon: Shield,
      title: 'Admin Control Panel',
      description: 'Comprehensive admin tools to manage flagged, removed, and allowed comments.',
    },
    {
      icon: Zap,
      title: 'Real-time Processing',
      description: 'Instant analysis and moderation of comments as they are submitted.',
    },
    {
      icon: Users,
      title: 'User Management',
      description: 'Role-based access control with user and admin permissions.',
    },
  ];

  const stats = [
    { label: 'Comments Processed', value: '10,000+', icon: MessageSquare },
    { label: 'Accuracy Rate', value: '95%', icon: TrendingUp },
    { label: 'Active Users', value: '500+', icon: Users },
    { label: 'Uptime', value: '99.9%', icon: CheckCircle },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="text-center py-20">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 animate-fadeIn">
            AI-Powered Comment
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {' '}Moderation
            </span>
          </h1>
          
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto animate-fadeIn">
            Keep your community safe with intelligent comment filtering, sentiment analysis, 
            and automated moderation powered by advanced AI technology.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fadeIn">
            {!isAuthenticated ? (
              <>
                <Link to="/register" className="btn-primary text-lg px-8 py-3">
                  Get Started
                </Link>
                <Link 
                  to="/login" 
                  className="btn-secondary text-lg px-8 py-3"
                >
                  Sign In
                </Link>
              </>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/comments" className="btn-primary text-lg px-8 py-3">
                  View Comments
                </Link>
                {user?.role === 'admin' && (
                  <Link to="/admin" className="btn-secondary text-lg px-8 py-3">
                    Admin Panel
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="glass rounded-lg p-6 text-center animate-fadeIn">
                <stat.icon className="w-8 h-8 text-blue-400 mx-auto mb-4" />
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-gray-400">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">
              Powerful Features
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Everything you need to maintain a healthy and engaging comment section
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="glass rounded-lg p-6 hover:bg-opacity-20 transition-all duration-300 animate-fadeIn">
                <feature.icon className="w-12 h-12 text-blue-400 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass rounded-2xl p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of users who trust our AI-powered moderation system
              </p>
              <Link to="/register" className="btn-primary text-lg px-8 py-3">
                Create Your Account
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Welcome Back Section for Authenticated Users */}
      {isAuthenticated && (
        <section className="py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div className="glass rounded-2xl p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Welcome back, {user?.name}!
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                {user?.role === 'admin' 
                  ? 'Manage your community with powerful admin tools'
                  : 'Share your thoughts and engage with the community'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/comments" className="btn-primary text-lg px-8 py-3">
                  View Comments
                </Link>
                {user?.role === 'admin' && (
                  <Link to="/admin" className="btn-secondary text-lg px-8 py-3">
                    Admin Dashboard
                  </Link>
                )}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default Home;
